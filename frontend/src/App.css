/* 全局样式 */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f0f2f5;
  color: rgba(0, 0, 0, 0.85);
}

/* 现代化表格样式 */
.ant-table {
  font-size: 14px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  border-radius: 10px;
  overflow: hidden;
}

.ant-table-thead > tr > th {
  background-color: #f8fafd !important;
  color: #0f172a;
  font-weight: 600;
  border-bottom: 1px solid #edf2f7;
  padding: 18px 24px;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.ant-table-tbody > tr > td {
  padding: 18px 24px;
  border-bottom: 1px solid #f0f5fa;
}

.table-row:hover {
  background-color: #f1f5f9 !important;
}

/* 卡片样式 */
.ant-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: none;
  transition: all 0.3s ease;
}

.ant-card.ant-card-hoverable:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

/* 小尺寸卡片样式（用于模型配置和任务卡片） */
.ant-card.ant-card-small {
  border-radius: 8px;
}

.ant-card-head {
  border-bottom: 1px solid #edf2f7;
}

/* 按钮样式 */
.ant-btn {
  border-radius: 8px;
  font-weight: 500;
  box-shadow: none;
  height: 38px;
  padding: 0 18px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.ant-btn-primary {
  background-color: #1677ff;
  border: none;
  box-shadow: 0 2px 6px rgba(22, 119, 255, 0.2);
}

.ant-btn-primary:hover, .ant-btn-primary:focus {
  background-color: #0958d9;
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(22, 119, 255, 0.25);
  border: none;
}


/* 菜单样式 */
.ant-menu {
  background: transparent;
}

.ant-menu-item {
  margin: 4px 0 !important;
  height: 40px !important;
  line-height: 40px !important;
  border-radius: 6px;
  font-size: 14px;
}

.ant-menu-submenu-title {
  font-size: 14px !important;
}

.ant-menu-item-selected {
  background: rgba(22, 119, 255, 0.08) !important;
  color: #1677ff !important;
  font-weight: 500;
}

.ant-menu-item:hover {
  color: #1677ff !important;
  background: rgba(22, 119, 255, 0.04) !important;
}

.ant-menu-item-selected:after {
  border-right: 2px solid #1677ff !important;
}

.ant-menu-submenu-title:hover {
  color: #1677ff !important;
  background: rgba(22, 119, 255, 0.04) !important;
}

.ant-menu-submenu-selected > .ant-menu-submenu-title {
  color: #1677ff !important;
}

.ant-menu-submenu-selected > .ant-menu-submenu-title:after {
  border-right: 2px solid #1677ff !important;
}

/* 模态窗口样式 */
.ant-modal-content {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
}

.ant-modal-header {
  border-bottom: 1px solid #f1f5f9;
  padding: 20px 24px;
}

.ant-modal-body {
  padding: 28px;
}

.ant-modal-footer {
  border-top: 1px solid #f1f5f9;
  padding: 16px 24px;
}

/* 标签页样式 */
.ant-tabs-tab {
  padding: 12px 4px !important;
  margin: 0 20px 0 0 !important;
  font-weight: 500;
}

.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #2563eb !important;
  font-weight: 600;
}

.ant-tabs-ink-bar {
  background-color: #2563eb !important;
  height: 3px !important;
  border-radius: 3px;
}

/* 滑块样式 */
.ant-slider-track {
  background-color: #3b82f6 !important;
}

.ant-slider-handle {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

/* 刷新表格加载状态 */
.ant-spin-dot-item {
  background-color: #3b82f6;
}

/* 统计卡片 */
.stat-card {
  padding: 24px;
  border-radius: 12px;
  background: #fff;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  height: 100%;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

.stat-card .stat-title {
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  margin-bottom: 12px;
}

.stat-card .stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #0f172a;
  line-height: 1.2;
  margin-bottom: 4px;
}

.stat-card .stat-description {
  font-size: 14px;
  color: #64748b;
}

/* 徽章样式 */
.status-badge {
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
}

.status-badge.active {
  background-color: rgba(34, 197, 94, 0.1);
  color: #16a34a;
}

.status-badge.inactive {
  background-color: rgba(100, 116, 139, 0.1);
  color: #64748b;
}

.status-badge.completed {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

/* 列表项样式 */
.modern-list-item {
  border-radius: 12px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
  background: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
  overflow: hidden;
}

.modern-list-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

/* 分割线样式 */
.ant-divider {
  margin: 28px 0;
}

.ant-divider-with-text {
  color: #0f172a;
  font-weight: 600;
}

/* 开关样式 */
.ant-switch {
  background-color: #cbd5e1;
}

.ant-switch-checked {
  background-color: #3b82f6 !important;
}

/* 分页器 */
.ant-pagination-item-active {
  border-color: #3b82f6 !important;
}

.ant-pagination-item-active a {
  color: #3b82f6 !important;
}

/* 表单标签 */
.ant-form-item-label > label {
  font-weight: 500;
  color: #334155;
}

.layout {
  min-height: 100vh;
}

.header {
  display: flex;
  align-items: center;
  background: #1890ff;
}

.content {
  padding: 24px;
  background: #f0f2f5;
  min-height: calc(100vh - 64px - 70px);
}

.setup-card {
  max-width: 600px;
  margin: 0 auto;
}

.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
}

/* 消息和对话样式已移至 frontend/src/pages/actiontask/css/conversation.css */

/* Ant Design 样式覆盖 */
.ant-btn {
  border-radius: 8px;
}

.ant-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.ant-table-wrapper {
  border-radius: 12px;
  overflow: hidden;
}

/* 状态样式 */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  font-size: 12px;
  border-radius: 10px;
  font-weight: 500;
}

.status-badge.active {
  background-color: #e6f7ff;
  color: #1677ff;
}

.status-badge.completed {
  background-color: #f6ffed;
  color: #52c41a;
}



/* 统计卡片样式 */
.stat-card {
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  height: 100%;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.stat-title {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #0f172a;
  margin-bottom: 4px;
}

.stat-description {
  font-size: 12px;
  color: #94a3b8;
}

/* 表格行样式 */
.table-row:hover {
  cursor: pointer;
}

.modern-table-cell {
  padding: 16px !important;
}

/* 工作空间表格行样式 */
.workspace-table-row td {
  vertical-align: middle !important;
  padding: 8px 16px !important;
}

/* 任务列表项悬停样式 */
.task-list-item:hover {
  background-color: #f5f5f5 !important;
}

/* 闪烁动画 */
@keyframes blink {
  0% { opacity: 1; }
  50% { opacity: 0; }
  100% { opacity: 1; }
}

/* 消息样式已移至 frontend/src/pages/actiontask/css/conversation.css */

/* 对话详情样式已移至 frontend/src/pages/actiontask/css/conversation.css */

.settings-panel {
  margin-top: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item span {
  color: #666;
}

/* Status Tags */
.ant-tag {
  border-radius: 4px;
  padding: 2px 8px;
}

.ant-tag-success {
  background: #f6ffed;
  border-color: #b7eb8f;
  color: #52c41a;
}

.ant-tag-default {
  background: #f5f5f5;
  border-color: #d9d9d9;
  color: #666;
}

/* Empty State */
.ant-empty {
  padding: 48px 0;
}

.ant-empty-image {
  height: 100px;
}

.ant-empty-description {
  color: #666;
}

/* Loading State */
.ant-spin {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* 响应式设计已移至 frontend/src/pages/actiontask/css/conversation.css */

/* 角色管理 - 能力和工具卡片样式 */
.capability-card, .tool-card {
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  margin-bottom: 16px;
}

.capability-card:hover, .tool-card:hover {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.capability-card .ant-card-head, .tool-card .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 16px;
}

.capability-card .ant-card-body, .tool-card .ant-card-body {
  padding: 16px;
}

.capability-card .ant-form-item, .tool-card .ant-form-item {
  margin-bottom: 8px;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.capability-card .ant-form-item:hover, .tool-card .ant-form-item:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.capability-card .ant-checkbox-wrapper, .tool-card .ant-checkbox-wrapper {
  width: 100%;
  display: flex;
  align-items: center;
}

/* 不同类型能力卡片的样式 */
.capability-card[data-type="core"] {
  border-left: 4px solid #1677ff;
}

.capability-card[data-type="advanced"] {
  border-left: 4px solid #722ed1;
}

.capability-card[data-type="supervision"] {
  border-left: 4px solid #fa8c16;
}

.capability-card[data-type="execution"] {
  border-left: 4px solid #eb2f96;
}

.capability-card[data-type="specialized"] {
  border-left: 4px solid #13c2c2;
}

/* 工具卡片分类样式 */
.tool-card[data-category="数据处理"] {
  border-left: 4px solid #1677ff;
}

.tool-card[data-category="信息获取"] {
  border-left: 4px solid #722ed1;
}

.tool-card[data-category="代码执行"] {
  border-left: 4px solid #52c41a;
}

.tool-card[data-category="文档处理"] {
  border-left: 4px solid #fa8c16;
}

.tool-card[data-category="系统集成"] {
  border-left: 4px solid #eb2f96;
}

.tool-card[data-category="其他"] {
  border-left: 4px solid #8c8c8c;
}

/* 行动空间卡片样式 */
.action-space-card {
  transition: all 0.3s;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.action-space-card .ant-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-bottom: 0;
}

.action-space-card .ant-card-actions {
  margin-top: auto;
  border-top: 1px solid #f0f0f0;
}

.action-space-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.09);
}

.add-action-space-card {
  transition: all 0.3s;
  height: 100%;
  border: 1px dashed #c0c0c0;
  background-color: #f7f7f7;
}

.add-action-space-card:hover {
  border-color: #91caff;
  color: #91caff;
  background-color: #f5f9ff;
}

/* 行动空间卡片封面渐变 */
.action-space-card-cover-gradient-1 {
  background: linear-gradient(135deg, #91caff 0%, #b8a3e8 100%);
}

.action-space-card-cover-gradient-2 {
  background: linear-gradient(135deg, #b5e8b0 0%, #a1d9d9 100%);
}

.action-space-card-cover-gradient-3 {
  background: linear-gradient(135deg, #ffe9ba 0%, #ffd0b5 100%);
}

.action-space-card-cover-gradient-4 {
  background: linear-gradient(135deg, #f5d6e6 0%, #d8c2e5 100%);
}

/* 标签溢出处理 */
.action-space-tags {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  min-height: 30px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

/* 新的标签容器样式 - 支持溢出显示 */
.action-space-tags-container {
  max-width: 100%;
  min-height: 30px;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  position: relative;
  cursor: pointer;
}

/* 当标签容器溢出时的样式 */
.action-space-tags-container::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 100%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.9));
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.2s ease;
}

/* 当容器内容溢出时显示渐变遮罩 */
.action-space-tags-container.overflow::after {
  opacity: 1;
}

/* 行动空间标签样式 */
.space-tag {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  margin: 0 4px 4px 0;
  font-size: 12px;
  border-radius: 12px;
  font-weight: 500;
  background-color: rgba(0, 0, 0, 0.05);
  color: rgba(0, 0, 0, 0.65);
  border: none;
}

.space-tag.industry {
  background-color: rgba(24, 144, 255, 0.1);
  color: rgba(24, 144, 255, 0.85);
}

.space-tag.scenario {
  background-color: rgba(82, 196, 26, 0.1);
  color: rgba(82, 196, 26, 0.85);
}

/* 行动空间标签筛选样式 */
.space-tag-filter {
  display: inline-flex;
  align-items: center;
  padding: 2px 10px;
  margin: 0 6px 6px 0;
  font-size: 12px;
  border-radius: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.65);
  border: 1px solid #f0f0f0;
}

.space-tag-filter:hover {
  background-color: #e6f7ff;
  color: #1890ff;
  border-color: #91d5ff;
}

.space-tag-filter.selected.industry {
  background-color: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  border-color: #91d5ff;
}

.space-tag-filter.selected.scenario {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
  border-color: #b7eb8f;
}

/* 代码块样式优化 */
pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-x: auto;
  max-width: 100%;
}

code {
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 消息框样式已移至 frontend/src/pages/actiontask/css/conversation.css */

/* 工具调用格式样式 */
pre code.language-json,
pre code.language-bash,
pre code.language-shell {
  display: block;
  white-space: pre-wrap;
  word-break: break-all;
  max-width: 100%;
}

/* 自动讨论面板动画和样式已移至 frontend/src/pages/actiontask/css/conversation.css */

/* 行动任务卡片样式 */
.task-card .ant-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 12px;
}

.task-card .task-info-section {
  margin-top: auto;
  padding: 0;
}

.task-card .task-info-section .ant-divider {
  margin: 16px 0;
  border-color: #edf2f7;
}

.task-card .task-info-section .info-content {
  padding: 4px 0;
}

.task-card .ant-card-actions {
  margin-top: auto;
  border-top: 1px solid #edf2f7;
}

/* 添加新任务卡片样式 */
.add-task-card:hover {
  border-color: #1677ff !important;
  background-color: #f5f9ff !important;
}

/* 加载动画样式 - 已弃用，建议使用 Ant Design 的 Spin 组件 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80vh;
  flex-direction: column;
  background: transparent; /* 修改为透明背景 */
  border-radius: 12px;
  box-shadow: none; /* 移除阴影 */
}

.loading-content {
  position: relative;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 30px;
}

.loading-text {
  margin-top: 20px;
  font-size: 16px;
  color: #1677ff;
  font-weight: 500;
}

.loading-dots {
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-dot {
  width: 10px;
  height: 10px;
  margin: 0 5px;
  border-radius: 50%;
  background-color: #1677ff;
  opacity: 0;
  animation: loadingDot 1.4s infinite ease-in-out;
}

.loading-dot:nth-child(1) {
  animation-delay: 0s;
}

.loading-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dot:nth-child(3) {
  animation-delay: 0.4s;
}

.loading-dot:nth-child(4) {
  animation-delay: 0.6s;
}

@keyframes loadingDot {
  0%, 100% {
    opacity: 0.2;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.loading-spinner {
  position: relative;
  width: 60px;
  height: 60px;
  margin-bottom: 20px;
}

.loading-spinner:before {
  content: '';
  box-sizing: border-box;
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3px solid transparent;
  border-top-color: #1677ff;
  border-bottom-color: #1677ff;
  animation: spinnerAnimation 1.2s linear infinite;
}

@keyframes spinnerAnimation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 智能体头像波纹动画效果已移至 frontend/src/pages/actiontask/css/conversation.css */